import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  ImageList,
  ImageListItem,
  InputAdornment,
  Skeleton,
  TextField,
  Typography,
  Tab,
  Tabs,
} from "@mui/material";
import Alert from "@mui/material/Alert";
import Chip from "@mui/material/Chip";
import CircularProgress from "@mui/material/CircularProgress";
import IconButton from "@mui/material/IconButton";
import Stack from "@mui/material/Stack";
import React, { useCallback, useEffect, useState } from "react";
import { StoreContext } from "../../store";
import { getUid } from "../../utils";
import { useLanguage } from "../../i18n/LanguageContext";
import {
  ImageSource,
  ImageFile,
  getImages,
  getImagesByTag,
  getImageTags,
} from "../../services/imageService";

// 图片加载优化的常量
const THUMBNAIL_WIDTH = 400; // 缩略图宽度
const IMAGE_QUALITY = 80; // 图片质量
const SKELETON_COUNT = 6; // 加载时显示的骨架屏数量

// 定义图片批次类型
interface ImageBatch {
  id: number; // 批次ID
  photos: ImageFile[]; // 一批图片
}

export const Images = () => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const [initialImages, setInitialImages] = useState<ImageFile[]>([]); // 用于存储初始加载的图片
  const [imageBatches, setImageBatches] = useState<ImageBatch[]>([]); // 存储每批加载的图片
  const [batchCounter, setBatchCounter] = useState(0); // 批次计数器
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [batchLoading, setBatchLoading] = useState<number | null>(null); // 跟踪当前正在加载的批次
  const [hasNextPage, setHasNextPage] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedQuery, setDebouncedQuery] = useState("");
  const [selectedTag, setSelectedTag] = useState("");
  const tagsContainerRef = React.useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [loadingImages, setLoadingImages] = useState<{
    [key: string]: boolean;
  }>({});
  const [imageSource, setImageSource] = useState<ImageSource>("pixabay"); // 默认使用Pixabay API
  const [errorMessage, setErrorMessage] = useState<string | null>(null); // 添加错误信息状态
  const [totalImages, setTotalImages] = useState(0); // 图片总数

  // 添加防抖效果
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // 优化 fetchImages 函数，使用imageService服务
  const fetchImages = useCallback(async () => {
    if (loading) return;
    setLoading(true);
    setErrorMessage(null);

    // 如果不是第一页，设置当前批次为加载中
    if (page > 1) {
      setBatchLoading(batchCounter + 1);
    }

    try {
      const searchTerm = selectedTag || debouncedQuery;
      const response = await getImages(imageSource, page, searchTerm);

      // 根据页码决定是设置初始图片还是添加新批次图片
      if (page === 1) {
        setInitialImages(response.images);
        setImageBatches([]);
        setBatchCounter(0);
        setTotalImages(response.total);
      } else {
        // 创建新的批次并添加到批次列表
        const newBatch: ImageBatch = {
          id: batchCounter + 1,
          photos: response.images,
        };
        setBatchCounter(batchCounter + 1);
        setImageBatches((prev) => [...prev, newBatch]);
      }

      setHasNextPage(response.images.length === 10); // 假设每页10条
      setPage(page + 1);

      // 如果没有找到图片，显示错误信息
      if (response.images.length === 0 && page === 1) {
        setErrorMessage(t("no_images_found_try_another"));
      }
    } catch (error) {
      console.error(`Error fetching images from ${imageSource}:`, error);
      const sourceName = imageSource === "pexels" ? "Pexels" : "Pixabay";
      setErrorMessage(t("loading_images_failed").replace("{0}", sourceName));
    } finally {
      setLoading(false);
      setBatchLoading(null);
    }
  }, [
    loading,
    page,
    selectedTag,
    debouncedQuery,
    batchCounter,
    imageSource,
    t,
  ]);

  // 添加搜索变化处理
  useEffect(() => {
    setInitialImages([]);
    setImageBatches([]);
    setBatchCounter(0);
    setPage(1);
    fetchImages();
  }, [debouncedQuery, selectedTag, imageSource]); // 当搜索词或API源变化时重新获取图片

  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLElement>) => {
      const target = event.target as HTMLElement;
      if (
        !loading &&
        hasNextPage &&
        target.scrollHeight - target.scrollTop <= target.clientHeight + 10
      ) {
        fetchImages();
      }
    },
    [loading, hasNextPage, fetchImages]
  );

  const handleAddImage = (
    src: string,
    imageId: number | string,
    imageMetadata?: any
  ) => {
    // Set loading state for this specific image
    setLoadingImages((prev) => ({ ...prev, [imageId.toString()]: true }));

    // First add the image to resources
    // const imageIndex = store.addImageResource(src);
    const id = getUid();

    const imageElement = document.createElement("img");
    imageElement.src = src;
    imageElement.id = `image-${id}`;
    imageElement.style.display = "none";

    document.body.appendChild(imageElement);

    imageElement.onload = () => {
      store.addImageElement(imageElement, id, imageMetadata);
      // Clear loading state
      setLoadingImages((prev) => ({ ...prev, [imageId.toString()]: false }));
    };

    imageElement.onerror = () => {
      // Handle error case
      setLoadingImages((prev) => ({ ...prev, [imageId.toString()]: false }));
      // Optionally show an error message
      setErrorMessage(t("no_images_found"));
    };
  };

  const checkScroll = useCallback(() => {
    const container = tagsContainerRef.current;
    if (container) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      );
    }
  }, []);

  const handleScrollLeft = () => {
    const container = tagsContainerRef.current;
    if (container) {
      container.scrollBy({ left: -200, behavior: "smooth" });
    }
  };

  const handleScrollRight = () => {
    const container = tagsContainerRef.current;
    if (container) {
      container.scrollBy({ left: 200, behavior: "smooth" });
    }
  };

  // Add this effect to monitor scroll position:
  useEffect(() => {
    const container = tagsContainerRef.current;
    if (container) {
      checkScroll();
      container.addEventListener("scroll", checkScroll);
      window.addEventListener("resize", checkScroll);
      return () => {
        container.removeEventListener("scroll", checkScroll);
        window.removeEventListener("resize", checkScroll);
      };
    }
  }, [checkScroll]);

  // 判断是否有图片
  const hasNoImages =
    !loading && initialImages.length === 0 && imageBatches.length === 0;

  // 渲染skeleton骨架屏
  const renderSkeletons = useCallback(() => {
    // 创建不同高度的skeleton数组，更像真实瀑布流
    const skeletonHeights = [180, 220, 260, 300, 340, 380];

    return (
      <ImageList variant="masonry" cols={2} gap={16} sx={{ margin: 0 }}>
        {Array.from(new Array(SKELETON_COUNT)).map((_, index) => (
          <ImageListItem key={`skeleton-${index}`}>
            <Skeleton
              animation="wave"
              variant="rectangular"
              height={skeletonHeights[index % skeletonHeights.length]}
              sx={{
                borderRadius: 2,
              }}
            />
          </ImageListItem>
        ))}
      </ImageList>
    );
  }, []);

  // 用于渲染单个图片项的函数
  const renderImageItem = (image: ImageFile, index: number) => (
    <ImageListItem
      key={`${image.id}-${index}`}
      onClick={() =>
        handleAddImage(image.src.original, image.id, {
          name: `${imageSource} Image ${index + 1}`,
          alt: image.alt,
          photographer: image.photographer,
        })
      }
      sx={{
        alt: image.alt,
        cursor: "pointer",
        mb: 1,
        position: "relative",
        transition: "all 0.3s ease",
        "& img": {
          borderRadius: 2,
          width: "100%",
          height: "100%",
          objectFit: "cover",
        },
        "&:hover": {
          transform: "translateY(-4px)",
          boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        },
      }}
    >
      <img
        src={`${image.src.medium}?auto=compress&w=${THUMBNAIL_WIDTH}&q=${IMAGE_QUALITY}`}
        alt={image.photographer || t("unknown")}
        loading="lazy"
        style={{ opacity: loadingImages[image.id.toString()] ? 0.5 : 1 }}
        srcSet={`
          ${
            image.src.medium
          }?auto=compress&w=${THUMBNAIL_WIDTH}&q=${IMAGE_QUALITY} 1x,
          ${image.src.large}?auto=compress&w=${
          THUMBNAIL_WIDTH * 2
        }&q=${IMAGE_QUALITY} 2x
        `}
      />
      {loadingImages[image.id.toString()] && (
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            zIndex: 1,
          }}
        >
          <CircularProgress size={24} />
        </Box>
      )}
    </ImageListItem>
  );

  // 渲染分隔标题
  const renderBatchTitle = (batchNumber: number) => (
    <Box
      sx={{
        height: "1px",
        bgcolor: "divider",
        my: 3,
        position: "relative",
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: -10,
          left: "50%",
          transform: "translateX(-50%)",
          bgcolor: "grey.100",
          px: 2,
        }}
      ></Box>
    </Box>
  );

  // 获取当前图片源对应的标签
  const getCurrentSourceTags = () => {
    const tags = getImageTags();
    const commonTags = tags.common;
    const sourceTags = imageSource === "pixabay" ? tags.pixabay : tags.pexels;
    return [...commonTags, ...sourceTags];
  };

  // 计算当前显示的图片总数
  const getCurrentDisplayedImagesCount = () => {
    return (
      initialImages.length +
      imageBatches.reduce((sum, batch) => sum + batch.photos.length, 0)
    );
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 2, // 增加圆角
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)", // 优化阴影效果
      }}
    >
      {/* 标题栏样式优化 */}
      <Box
        sx={{
          bgcolor: "grey.100",
          height: 56,
          display: "flex",
          alignItems: "center",
          px: 3,
          flexShrink: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
          {t("image_library")}
        </Typography>
      </Box>

      {/* 添加Tab标签切换不同的图片源 */}
      <Tabs
        value={imageSource}
        onChange={(_, newValue) => {
          setImageSource(newValue);
          setErrorMessage(null);
          setInitialImages([]);
          setImageBatches([]);
          setSelectedTag("");
          setSearchQuery("");
          setHasNextPage(true);
          setPage(1);
        }}
        sx={{ borderBottom: 1, borderColor: "divider", bgcolor: "grey.100" }}
      >
        <Tab
          label={
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <img
                src="/assets/icons/px.png"
                alt="Pixabay"
                style={{ width: 20, height: 20 }}
              />
              <Typography variant="body2">Pixabay</Typography>
            </Box>
          }
          value="pixabay"
        />
        <Tab
          label={
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <img
                src="/assets/icons/pexels.png"
                alt="Pexels"
                style={{ width: 20, height: 20 }}
              />
              <Typography variant="body2">Pexels</Typography>
            </Box>
          }
          value="pexels"
        />
      </Tabs>

      {/* 搜索框和标签区域 */}
      <Box sx={{ p: 2, bgcolor: "grey.100" }}>
        <TextField
          fullWidth
          size="small"
          placeholder={t("search_images")}
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setSelectedTag("");
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              bgcolor: "background.paper",
              "&:hover": {
                "& > fieldset": { borderColor: "primary.main" },
              },
            },
          }}
        />
        <Box
          sx={{
            width: 250,
            position: "relative",
            mt: 2,
            "&:hover .scroll-button": {
              display: "flex",
            },
          }}
        >
          <Box
            sx={{
              display: "flex",
              overflowX: "auto",
              scrollBehavior: "smooth",
              "&::-webkit-scrollbar": { display: "none" },
              msOverflowStyle: "none",
              scrollbarWidth: "none",
              px: 1,
              borderRadius: 2,
            }}
            ref={tagsContainerRef}
          >
            <Stack direction="row" spacing={1}>
              {getCurrentSourceTags().map((tag) => (
                <Chip
                  key={tag}
                  label={tag}
                  onClick={() => {
                    setSelectedTag(selectedTag === tag ? "" : tag);
                    setSearchQuery("");
                  }}
                  color={selectedTag === tag ? "primary" : "default"}
                  sx={{ whiteSpace: "nowrap" }}
                />
              ))}
            </Stack>
          </Box>

          <IconButton
            className="scroll-button"
            sx={{
              position: "absolute",
              left: 0,
              top: "50%",
              transform: "translateY(-50%)",
              bgcolor: "background.paper",
              opacity: 0.7,
              "&:hover": { bgcolor: "background.paper", opacity: 1 },
              display: "none",
            }}
            size="small"
            onClick={handleScrollLeft}
          >
            <ChevronLeftIcon fontSize="small" />
          </IconButton>

          <IconButton
            className="scroll-button"
            sx={{
              position: "absolute",
              right: 0,
              top: "50%",
              transform: "translateY(-50%)",
              bgcolor: "background.paper",
              opacity: 0.7,
              "&:hover": { bgcolor: "background.paper", opacity: 1 },
              display: "none",
            }}
            size="small"
            onClick={handleScrollRight}
          >
            <ChevronRightIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      {/* 错误消息显示 */}
      {errorMessage && !loading && (
        <Alert severity="warning" sx={{ m: 1, mb: 0 }}>
          {errorMessage}
        </Alert>
      )}

      {/* 图片列表区域 */}
      <Box
        onScroll={handleScroll}
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          overflow: "auto",
          px: 2,
          "&::-webkit-scrollbar": {
            width: "5px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            borderRadius: "5px",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.3)",
            },
          },
        }}
      >
        {/* 添加无结果提示 */}
        {hasNoImages && !loading && !errorMessage && (
          <Alert severity="info" sx={{ mb: 2, mt: 2 }}>
            {t("no_images_found")}
          </Alert>
        )}

        {/* 初始加载的skeleton */}
        {loading && page === 1 && renderSkeletons()}

        {/* 初始图片列表 - 保持顺序不变 */}
        {initialImages.length > 0 && (
          <ImageList
            variant="masonry"
            cols={2}
            gap={16}
            sx={{ margin: 0, mt: 2 }}
          >
            {initialImages.map((image, index) => renderImageItem(image, index))}
          </ImageList>
        )}

        {/* 每批加载的图片单独显示，位置锁定 */}
        {imageBatches.map((batch) => (
          <Box key={`batch-${batch.id}`}>
            {renderBatchTitle(batch.id)}
            <ImageList variant="masonry" cols={2} gap={16} sx={{ margin: 0 }}>
              {batch.photos.map((image, index) => {
                const imageIndex =
                  index + initialImages.length + (batch.id - 1) * 10; // 每批10张图片
                return renderImageItem(image, imageIndex);
              })}
            </ImageList>
          </Box>
        ))}

        {/* 显示当前正在加载的批次的skeleton */}
        {batchLoading !== null && (
          <>
            {renderBatchTitle(batchLoading)}
            {renderSkeletons()}
          </>
        )}

        {/* 结束指示器（当没有更多加载时） */}
        {!hasNextPage && initialImages.length > 0 && (
          <Box
            sx={{
              p: 2,
              textAlign: "center",
              borderTop: "1px solid",
              borderColor: "divider",
              mt: 2,
            }}
          >
            <Typography variant="caption" color="text.secondary">
              {t("displayed_results")
                .replace("{0}", getCurrentDisplayedImagesCount().toString())
                .replace(
                  "{1}",
                  totalImages ? totalImages.toString() : t("unknown")
                )}
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};
