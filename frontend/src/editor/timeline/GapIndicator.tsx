"use client";
import React, { useState, useCallback, useMemo } from "react";
import { Box, IconButton, Tooltip } from "@mui/material";
import { Delete as DeleteIcon } from "@mui/icons-material";
import { observer } from "mobx-react";
import { useGlobalElementDragState } from "./TrackDndContext";

interface TimeGap {
  id: string;
  startTime: number;
  endTime: number;
  duration: number;
  trackId: string;
  beforeElementId?: string;
  afterElementId?: string;
}

interface GapIndicatorProps {
  gap: TimeGap;
  containerWidth: number;
  timelineDisplayDuration: number;
  timelinePanOffsetX: number;
  onDeleteGap: (gap: TimeGap) => void;
}

/**
 * 计算时间线位置的工具函数
 */
const calculateTimelinePosition = (
  time: number,
  displayDuration: number,
  offsetX: number,
  containerWidth: number
): number => {
  const relativeTime = time - offsetX;
  return (relativeTime / displayDuration) * 100;
};

export const GapIndicator: React.FC<GapIndicatorProps> = observer(
  ({
    gap,
    containerWidth,
    timelineDisplayDuration,
    timelinePanOffsetX,
    onDeleteGap,
  }) => {
    const [isHovered, setIsHovered] = useState(false);
    const isElementDragging = useGlobalElementDragState();

    // 计算间隙的位置和宽度
    const positionStyles = useMemo(() => {
      if (!containerWidth) {
        return {
          width: "0%",
          left: "0%",
          display: "none",
        };
      }

      const width =
        ((gap.endTime - gap.startTime) / timelineDisplayDuration) * 100;
      const left = calculateTimelinePosition(
        gap.startTime,
        timelineDisplayDuration,
        timelinePanOffsetX,
        containerWidth
      );

      // 如果间隙在可视区域外，不显示
      if (left > 100 || left + width < 0) {
        return {
          width: "0%",
          left: "0%",
          display: "none",
        };
      }

      return {
        width: `${Math.max(width, 0)}%`,
        left: `${Math.max(left, 0)}%`,
        display: "block",
      };
    }, [
      gap.startTime,
      gap.endTime,
      timelineDisplayDuration,
      timelinePanOffsetX,
      containerWidth,
    ]);

    // 处理删除间隙
    const handleDeleteGap = useCallback(
      (event: React.MouseEvent) => {
        event.preventDefault();
        event.stopPropagation();
        onDeleteGap(gap);
      },
      [gap, onDeleteGap]
    );

    // 处理鼠标悬停
    const handleMouseEnter = useCallback(() => {
      setIsHovered(true);
    }, []);

    const handleMouseLeave = useCallback(() => {
      setIsHovered(false);
    }, []);

    // 格式化时间显示
    const formatDuration = useCallback((duration: number) => {
      const seconds = Math.round((duration / 1000) * 10) / 10;
      return `${seconds}s`;
    }, []);

    // 如果间隙太小（小于100ms）或正在拖拽元素，不显示
    if (gap.duration < 100 || isElementDragging) {
      return null;
    }

    return (
      <Box
        sx={{
          position: "absolute",
          top: "2px",
          height: "calc(100% - 4px)",
          ...positionStyles,
          backgroundColor: isHovered
            ? "linear-gradient(135deg, rgba(156, 163, 175, 0.8), rgba(209, 213, 219, 0.6))"
            : "transparent",
          border: isHovered ? "1px solid rgba(107, 114, 128, 0.4)" : "none",
          borderRadius: "8px",
          cursor: isHovered ? "pointer" : "default",
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          minWidth: "20px",
          zIndex: 10,
          // 添加斜纹背景效果
          backgroundImage: isHovered
            ? `repeating-linear-gradient(
                45deg,
                rgba(156, 163, 175, 0.3),
                rgba(156, 163, 175, 0.3) 3px,
                rgba(209, 213, 219, 0.2) 3px,
                rgba(209, 213, 219, 0.2) 6px
              )`
            : "none",
          "&:hover": {
            backgroundColor:
              "linear-gradient(135deg, rgba(156, 163, 175, 0.9), rgba(209, 213, 219, 0.7))",
            border: "1px solid rgba(107, 114, 128, 0.6)",
            cursor: "pointer",
            transform: "translateY(-1px)",
            boxShadow:
              "0 4px 12px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.3)",
            backgroundImage: `repeating-linear-gradient(
              45deg,
              rgba(156, 163, 175, 0.4),
              rgba(156, 163, 175, 0.4) 3px,
              rgba(209, 213, 219, 0.3) 3px,
              rgba(209, 213, 219, 0.3) 6px
            )`,
          },
        }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* 删除按钮 */}
        {isHovered && (
          <Tooltip title="删除间隙" placement="top">
            <IconButton size="small" onClick={handleDeleteGap}>
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    );
  }
);

GapIndicator.displayName = "GapIndicator";
